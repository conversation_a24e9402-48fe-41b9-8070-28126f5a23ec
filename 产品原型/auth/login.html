<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 柴管家</title>
    <link rel="stylesheet" href="../assets/css/material-design.css">
    <link rel="stylesheet" href="../assets/css/common.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--md-sys-color-primary-container) 0%, var(--md-sys-color-surface) 100%);
        }
        
        .login-card {
            width: 100%;
            max-width: 400px;
            padding: 32px;
            background-color: var(--md-sys-color-surface);
            border-radius: 16px;
            box-shadow: var(--md-elevation-3);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .login-logo {
            width: 64px;
            height: 64px;
            background-color: var(--md-sys-color-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
        }
        
        .login-form {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .login-tabs {
            display: flex;
            background-color: var(--md-sys-color-surface-container);
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 24px;
        }
        
        .login-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            font-weight: 500;
        }
        
        .login-tab.active {
            background-color: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
        }
        
        .login-tab:not(.active) {
            color: var(--md-sys-color-on-surface-variant);
        }
        
        .form-group {
            position: relative;
        }
        
        .form-input {
            width: 100%;
            padding: 16px;
            border: 1px solid var(--md-sys-color-outline);
            border-radius: 8px;
            font-size: 16px;
            background-color: var(--md-sys-color-surface);
            color: var(--md-sys-color-on-surface);
            transition: border-color 0.2s ease-in-out;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--md-sys-color-primary);
            border-width: 2px;
        }
        
        .form-label {
            position: absolute;
            top: -8px;
            left: 12px;
            background-color: var(--md-sys-color-surface);
            padding: 0 4px;
            font-size: 12px;
            color: var(--md-sys-color-primary);
            font-weight: 500;
        }
        
        .verification-group {
            display: flex;
            gap: 12px;
        }
        
        .verification-input {
            flex: 1;
        }
        
        .verification-btn {
            padding: 16px 20px;
            white-space: nowrap;
        }
        
        .verification-btn:disabled {
            background-color: var(--md-sys-color-surface-container);
            color: var(--md-sys-color-on-surface-variant);
            cursor: not-allowed;
        }
        
        .login-actions {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-top: 24px;
        }
        
        .login-links {
            display: flex;
            justify-content: space-between;
            margin-top: 16px;
        }
        
        .login-link {
            color: var(--md-sys-color-primary);
            text-decoration: none;
            font-size: 14px;
        }
        
        .login-link:hover {
            text-decoration: underline;
        }
        
        .error-message {
            color: var(--md-sys-color-error);
            font-size: 14px;
            margin-top: 8px;
            display: none;
        }
        
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body class="md-theme-light">
    <div class="login-container">
        <div class="login-card">
            <!-- Header -->
            <div class="login-header">
                <div class="login-logo">
                    <span class="material-icons" style="color: var(--md-sys-color-on-primary); font-size: 32px;">chat</span>
                </div>
                <h1 class="md-headline-small">欢迎回来</h1>
                <p class="md-body-medium" style="color: var(--md-sys-color-on-surface-variant);">登录您的柴管家账户</p>
            </div>

            <!-- Login Form -->
            <form class="login-form" id="loginForm">
                <!-- Login Tabs -->
                <div class="login-tabs">
                    <div class="login-tab active" data-tab="verification">验证码登录</div>
                    <div class="login-tab" data-tab="password">密码登录</div>
                </div>

                <!-- Phone Number -->
                <div class="form-group">
                    <label class="form-label">手机号</label>
                    <input type="tel" class="form-input" id="phoneNumber" placeholder="请输入手机号" maxlength="11">
                    <div class="error-message" id="phoneError"></div>
                </div>

                <!-- Verification Code Login -->
                <div id="verificationLogin">
                    <div class="form-group">
                        <div class="verification-group">
                            <div class="verification-input">
                                <label class="form-label">验证码</label>
                                <input type="text" class="form-input" id="verificationCode" placeholder="请输入验证码" maxlength="6">
                            </div>
                            <button type="button" class="md-button md-button-outlined verification-btn" id="getCodeBtn">
                                获取验证码
                            </button>
                        </div>
                        <div class="error-message" id="codeError"></div>
                    </div>
                </div>

                <!-- Password Login -->
                <div id="passwordLogin" class="hidden">
                    <div class="form-group">
                        <label class="form-label">密码</label>
                        <input type="password" class="form-input" id="password" placeholder="请输入密码">
                        <div class="error-message" id="passwordError"></div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="login-actions">
                    <button type="submit" class="md-button md-button-filled" style="padding: 16px;">
                        <span class="material-icons">login</span>
                        登录
                    </button>
                </div>

                <!-- Links -->
                <div class="login-links">
                    <a href="#" class="login-link" id="forgotPassword">忘记密码？</a>
                    <a href="register.html" class="login-link">立即注册</a>
                </div>
            </form>
        </div>
    </div>

    <script src="../assets/js/common.js"></script>
    <script>
        class LoginPage {
            constructor() {
                this.currentTab = 'verification';
                this.countdownTimer = null;
                this.init();
            }

            init() {
                this.bindEvents();
                this.validateForm();
            }

            bindEvents() {
                // Tab切换
                document.querySelectorAll('.login-tab').forEach(tab => {
                    tab.addEventListener('click', (e) => {
                        this.switchTab(e.target.dataset.tab);
                    });
                });

                // 获取验证码
                document.getElementById('getCodeBtn').addEventListener('click', () => {
                    this.getVerificationCode();
                });

                // 表单提交
                document.getElementById('loginForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleLogin();
                });

                // 实时验证
                document.getElementById('phoneNumber').addEventListener('input', () => {
                    this.validatePhone();
                });

                document.getElementById('verificationCode').addEventListener('input', () => {
                    this.validateCode();
                });

                document.getElementById('password').addEventListener('input', () => {
                    this.validatePassword();
                });

                // 忘记密码
                document.getElementById('forgotPassword').addEventListener('click', (e) => {
                    e.preventDefault();
                    toast.info('密码重置功能开发中...');
                });
            }

            switchTab(tab) {
                this.currentTab = tab;
                
                // 更新tab样式
                document.querySelectorAll('.login-tab').forEach(t => {
                    t.classList.toggle('active', t.dataset.tab === tab);
                });

                // 切换表单内容
                document.getElementById('verificationLogin').classList.toggle('hidden', tab !== 'verification');
                document.getElementById('passwordLogin').classList.toggle('hidden', tab !== 'password');

                // 清除错误信息
                this.clearErrors();
            }

            validatePhone() {
                const phone = document.getElementById('phoneNumber').value;
                const phoneRegex = /^1[3-9]\d{9}$/;
                const errorEl = document.getElementById('phoneError');

                if (!phone) {
                    this.showError(errorEl, '请输入手机号');
                    return false;
                }

                if (!phoneRegex.test(phone)) {
                    this.showError(errorEl, '手机号格式不正确，请输入11位数字');
                    return false;
                }

                this.hideError(errorEl);
                return true;
            }

            validateCode() {
                const code = document.getElementById('verificationCode').value;
                const errorEl = document.getElementById('codeError');

                if (this.currentTab !== 'verification') return true;

                if (!code) {
                    this.showError(errorEl, '请输入验证码');
                    return false;
                }

                if (!/^\d{6}$/.test(code)) {
                    this.showError(errorEl, '验证码格式不正确');
                    return false;
                }

                this.hideError(errorEl);
                return true;
            }

            validatePassword() {
                const password = document.getElementById('password').value;
                const errorEl = document.getElementById('passwordError');

                if (this.currentTab !== 'password') return true;

                if (!password) {
                    this.showError(errorEl, '请输入密码');
                    return false;
                }

                if (password.length < 8) {
                    this.showError(errorEl, '密码长度至少8位');
                    return false;
                }

                this.hideError(errorEl);
                return true;
            }

            validateForm() {
                const phoneValid = this.validatePhone();
                const codeValid = this.currentTab === 'verification' ? this.validateCode() : true;
                const passwordValid = this.currentTab === 'password' ? this.validatePassword() : true;

                return phoneValid && codeValid && passwordValid;
            }

            async getVerificationCode() {
                if (!this.validatePhone()) return;

                const btn = document.getElementById('getCodeBtn');
                const phone = document.getElementById('phoneNumber').value;

                try {
                    loading.show('发送验证码中...');
                    
                    // 模拟API调用
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    // 模拟检查手机号是否注册
                    if (phone === '13900139000') {
                        throw new Error('该手机号未注册，请先注册');
                    }

                    loading.hide();
                    toast.success('验证码已发送');
                    this.startCountdown(btn);

                } catch (error) {
                    loading.hide();
                    toast.error(error.message);
                }
            }

            startCountdown(btn) {
                let count = 60;
                btn.disabled = true;
                btn.textContent = `${count}秒后重试`;

                this.countdownTimer = setInterval(() => {
                    count--;
                    btn.textContent = `${count}秒后重试`;

                    if (count <= 0) {
                        clearInterval(this.countdownTimer);
                        btn.disabled = false;
                        btn.textContent = '获取验证码';
                    }
                }, 1000);
            }

            async handleLogin() {
                if (!this.validateForm()) return;

                const phone = document.getElementById('phoneNumber').value;
                const formData = { phone, loginType: this.currentTab };

                if (this.currentTab === 'verification') {
                    formData.code = document.getElementById('verificationCode').value;
                } else {
                    formData.password = document.getElementById('password').value;
                }

                try {
                    loading.show('登录中...');
                    
                    // 模拟API调用
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    
                    // 模拟登录验证
                    if (this.currentTab === 'verification' && formData.code !== '123456') {
                        throw new Error('验证码错误，请重新输入');
                    }
                    
                    if (this.currentTab === 'password' && formData.password !== 'password123') {
                        throw new Error('密码错误，请重新输入');
                    }

                    loading.hide();
                    toast.success('登录成功！');
                    
                    // 跳转到工作台
                    setTimeout(() => {
                        window.location.href = '../dashboard/main.html';
                    }, 1000);

                } catch (error) {
                    loading.hide();
                    toast.error(error.message);
                }
            }

            showError(element, message) {
                element.textContent = message;
                element.style.display = 'block';
            }

            hideError(element) {
                element.style.display = 'none';
            }

            clearErrors() {
                document.querySelectorAll('.error-message').forEach(el => {
                    el.style.display = 'none';
                });
            }
        }

        // 初始化登录页面
        new LoginPage();
    </script>
</body>
</html>
